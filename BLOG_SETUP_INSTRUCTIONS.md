# Настройка отображения всех блог постов

## Обзор
Была создана функциональность для отображения всех статей из всех блогов на одной странице. Это особенно полезно для создания страницы "Все новости" или сводной страницы блога.

## Что было сделано

### 1. Модификация секции Custom Blogs Grid
Файл: `blocks/Blog-post.liquid`

**Добавленная функциональность:**
- Новая опция "Show all blogs" в настройках секции
- Поле для указания handles блогов (разделенных запятыми)
- Логика для сбора и сортировки статей из всех указанных блогов по дате публикации

**Новые настройки:**
- `show_all_blogs` (checkbox) - включает режим отображения всех блогов
- `blog_handles` (text) - список handles блогов через запятую (например: "news,updates,announcements")

### 2. Создание специального template для блога news
Файл: `templates/blog.news.json`

Этот template автоматически настроен для отображения всех статей из всех блогов.

### 3. Создание template для страницы news
Файл: `templates/page.news.json`

Альтернативный вариант для создания отдельной страницы новостей.

## Как использовать

### Вариант 1: Использование блога news
1. В Shopify Admin создайте блог с handle "news"
2. Перейдите в Online Store > Themes > Customize
3. Откройте страницу блога news (`/blogs/news`)
4. Секция "All Blogs Grid" уже настроена для отображения всех статей
5. При необходимости измените список блогов в настройке "Blog handles"

### Вариант 2: Добавление секции на любую страницу
1. Откройте любую страницу в редакторе тем
2. Добавьте секцию "Custom Blogs Grid"
3. В настройках секции:
   - Включите "Show all blogs on /blogs/news"
   - В поле "Blog handles" укажите handles ваших блогов через запятую
   - Настройте остальные параметры отображения

### Вариант 3: Создание отдельной страницы
1. В Shopify Admin создайте новую страницу с handle "news"
2. Назначьте ей template "page.news"
3. Страница будет доступна по адресу `/pages/news`

## Настройка handles блогов

В поле "Blog handles" укажите handles ваших блогов через запятую. Например:
- `news,updates,announcements`
- `blog,company-news,product-updates`

**Важно:** Используйте точные handles блогов, как они указаны в URL (например, если блог доступен по адресу `/blogs/company-news`, то handle будет "company-news").

## Как найти handle блога
1. Перейдите в Shopify Admin > Online Store > Blog posts
2. Выберите нужный блог
3. В настройках блога найдите поле "Search engine listing preview"
4. Handle блога - это часть URL после `/blogs/`

## Сортировка статей
Статьи автоматически сортируются по дате публикации (новые сначала) независимо от того, из какого блога они взяты.

## Ограничения
- Максимальное количество отображаемых статей настраивается в поле "Articles to show"
- Handles блогов должны быть указаны вручную в настройках
- Функция работает только с существующими блогами

## Стилизация
Все статьи отображаются в едином стиле, используя существующие настройки дизайна секции Custom Blogs Grid.
